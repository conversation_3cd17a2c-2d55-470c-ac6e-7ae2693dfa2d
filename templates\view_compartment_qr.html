{% extends "base.html" %}

{% block title %}Compartment {{ compartment_qr.compartment_number }} QR Code - Taluk Office{% endblock %}

{% block styles %}
<style>
:root {
    --gov-primary: #1e40af;
    --gov-secondary: #1e3a8a;
    --gov-accent: #f59e0b;
    --gov-success: #059669;
    --gov-warning: #d97706;
    --gov-danger: #dc2626;
    --gov-gray-50: #f8fafc;
    --gov-gray-100: #f1f5f9;
    --gov-gray-200: #e2e8f0;
    --gov-gray-300: #cbd5e1;
    --gov-gray-600: #475569;
    --gov-gray-700: #334155;
    --gov-gray-800: #1e293b;
    --gov-gray-900: #0f172a;
    --gov-white: #ffffff;
    --gov-border: var(--gov-gray-200);
    --gov-text: var(--gov-gray-800);
    --gov-gradient: linear-gradient(135deg, var(--gov-primary), var(--gov-secondary));
    --gov-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --gov-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --gov-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --gov-radius-md: 0.375rem;
    --gov-radius-lg: 0.5rem;
    --gov-radius-xl: 0.75rem;
    --gov-transition: all 0.3s ease;
}

.official-header {
    background: var(--gov-gradient);
    color: var(--gov-white);
    padding: 2.5rem 2rem;
    border-radius: var(--gov-radius-xl);
    margin-bottom: 2rem;
    box-shadow: var(--gov-shadow-lg);
    position: relative;
    overflow: hidden;
}

.official-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--gov-accent), var(--gov-success), var(--gov-accent));
}

.header-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 2rem;
    align-items: center;
}

.header-title {
    font-size: 2.25rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
}

.header-actions {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;
}

.official-btn {
    min-width: 160px;
    height: 44px;
    background: rgba(255, 255, 255, 0.15);
    color: var(--gov-white);
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 0 1.25rem;
    border-radius: var(--gov-radius-lg);
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--gov-transition);
    backdrop-filter: blur(10px);
    font-size: 0.875rem;
    line-height: 1;
    white-space: nowrap;
}

.official-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: var(--gov-shadow-md);
    text-decoration: none;
    color: var(--gov-white);
}

.compartment-info-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.qr-display-card {
    background: var(--gov-white);
    border-radius: var(--gov-radius-xl);
    box-shadow: var(--gov-shadow-md);
    border: 1px solid var(--gov-border);
    padding: 2rem;
    text-align: center;
    height: fit-content;
}

.qr-image-large {
    max-width: 280px;
    height: auto;
    border: 3px solid var(--gov-border);
    border-radius: var(--gov-radius-lg);
    padding: 1.5rem;
    background: var(--gov-white);
    margin-bottom: 1.5rem;
    box-shadow: var(--gov-shadow-sm);
}

.qr-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.qr-actions .action-btn {
    width: 100%;
    max-width: 200px;
}

.compartment-details-card {
    background: var(--gov-white);
    border-radius: var(--gov-radius-xl);
    box-shadow: var(--gov-shadow-md);
    border: 1px solid var(--gov-border);
    padding: 2rem;
    height: fit-content;
}

.details-section {
    margin-bottom: 2rem;
}

.details-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gov-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--gov-gray-100);
}

.section-description {
    color: var(--gov-gray-600);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.info-item {
    background: var(--gov-gray-50);
    padding: 1rem;
    border-radius: var(--gov-radius-lg);
    border: 1px solid var(--gov-border);
    transition: var(--gov-transition);
}

.info-item:hover {
    background: var(--gov-gray-100);
    border-color: var(--gov-primary);
}

.info-label {
    font-size: 0.75rem;
    color: var(--gov-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.info-value {
    font-size: 1.125rem;
    color: var(--gov-text);
    font-weight: 700;
}

.action-btn {
    min-width: 140px;
    height: 44px;
    padding: 0 1.25rem;
    border: none;
    border-radius: var(--gov-radius-lg);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: var(--gov-transition);
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1;
    box-shadow: var(--gov-shadow-sm);
    white-space: nowrap;
    border: 2px solid transparent;
}

.action-btn.primary {
    background: var(--gov-gradient);
    color: var(--gov-white);
    border-color: var(--gov-primary);
}

.action-btn.secondary {
    background: var(--gov-white);
    color: var(--gov-gray-700);
    border-color: var(--gov-border);
}

.action-btn.success {
    background: linear-gradient(135deg, var(--gov-success), #047857);
    color: var(--gov-white);
    border-color: var(--gov-success);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--gov-shadow-md);
    text-decoration: none;
    color: inherit;
}

.action-btn.primary:hover {
    background: var(--gov-secondary);
    border-color: var(--gov-secondary);
}

.action-btn.secondary:hover {
    background: var(--gov-gray-50);
    border-color: var(--gov-primary);
    color: var(--gov-primary);
}

.action-btn.success:hover {
    background: var(--gov-success);
    border-color: var(--gov-success);
}

.bundle-navigation-section {
    background: var(--gov-white);
    border-radius: var(--gov-radius-xl);
    box-shadow: var(--gov-shadow-md);
    border: 1px solid var(--gov-border);
    padding: 2rem;
    margin-bottom: 2rem;
}

.bundle-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(65px, 1fr));
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.bundle-item {
    background: var(--gov-gray-100);
    border: 2px solid var(--gov-border);
    border-radius: var(--gov-radius-md);
    padding: 1rem 0.5rem;
    text-align: center;
    font-weight: 700;
    color: var(--gov-gray-700);
    text-decoration: none;
    transition: var(--gov-transition);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 55px;
    font-size: 0.875rem;
}

.bundle-item:hover {
    background: var(--gov-primary);
    color: var(--gov-white);
    border-color: var(--gov-primary);
    transform: translateY(-2px);
    box-shadow: var(--gov-shadow-md);
    text-decoration: none;
}

.bundle-qr-management {
    background: var(--gov-white);
    border-radius: var(--gov-radius-xl);
    box-shadow: var(--gov-shadow-md);
    border: 1px solid var(--gov-border);
    padding: 2rem;
    margin-bottom: 2rem;
}

.qr-actions-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
    align-items: center;
}

.qr-actions-grid .action-btn {
    width: 100%;
    min-width: unset;
}

.bundle-qr-preview {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1rem;
    margin-top: 1.5rem;
}

.bundle-qr-card .action-btn {
    min-width: 32px;
    width: 32px;
    height: 32px;
    padding: 0;
    font-size: 0.75rem;
    border-radius: var(--gov-radius-md);
}

.bundle-qr-card .action-btn i {
    margin: 0;
}

.qr-data-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 2rem;
}

.qr-data-content {
    padding: 2rem;
}

.json-display {
    background: var(--gray-900);
    color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--radius-md);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    white-space: pre-wrap;
    line-height: 1.5;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
    cursor: pointer;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn.success {
    background: var(--gradient-success);
    color: white;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: inherit;
}

@media (max-width: 768px) {
    .compartment-info-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .header-actions {
        justify-content: center;
    }

    .bundle-grid {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    }

    .qr-actions-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .bundle-qr-preview {
        grid-template-columns: repeat(3, 1fr);
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .header-title {
        font-size: 1.75rem;
    }

    .official-header {
        padding: 2rem 1.5rem;
    }

    .action-btn {
        min-width: 120px;
        font-size: 0.8rem;
    }

    .official-btn {
        min-width: 140px;
        font-size: 0.8rem;
    }

    .qr-actions .action-btn {
        max-width: 180px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Official Header -->
    <div class="official-header">
        <div class="header-content">
            <div>
                <h1 class="header-title">
                    <i class="fas fa-archive"></i>
                    Compartment {{ compartment_qr.compartment_number }} QR Code
                </h1>
                <p class="header-subtitle">
                    Bundle Range: {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }} •
                    Total Bundles: {{ compartment_qr.get_bundle_list()|length }} •
                    Status: {% if compartment_qr.is_active %}Active{% else %}Inactive{% endif %}
                </p>
            </div>
            <div class="header-actions">
                <a href="{{ url_for('compartment_qr_management') }}" class="official-btn">
                    <i class="fas fa-arrow-left"></i>Back to QR Management
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content Grid -->
    <div class="compartment-info-grid">
        <!-- QR Code Display -->
        <div class="qr-display-card">
            <h3 class="section-title">
                <i class="fas fa-qrcode"></i>QR Code
            </h3>
            <p class="section-description">
                Scan this QR code to access compartment information and bundle navigation.
            </p>

            <img src="{{ url_for('get_compartment_qr_image', compartment_number=compartment_qr.compartment_number) }}"
                 alt="Compartment {{ compartment_qr.compartment_number }} QR Code"
                 class="qr-image-large">

            <div class="qr-actions">
                <a href="{{ url_for('download_compartment_qr', compartment_number=compartment_qr.compartment_number) }}"
                   class="action-btn primary">
                    <i class="fas fa-download"></i>Download PNG
                </a>
                <button onclick="printQR()" class="action-btn secondary">
                    <i class="fas fa-print"></i>Print QR Code
                </button>
                <button onclick="copyQRData()" class="action-btn success">
                    <i class="fas fa-copy"></i>Copy QR Data
                </button>
            </div>
        </div>

        <!-- Compartment Details -->
        <div class="compartment-details-card">
            <div class="details-section">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>Compartment Information
                </h3>
                <p class="section-description">
                    Official compartment details and organizational information for physical file storage management.
                </p>

                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Compartment Number</div>
                        <div class="info-value">{{ compartment_qr.compartment_number }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Total Bundles</div>
                        <div class="info-value">{{ compartment_qr.get_bundle_list()|length }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Created Date</div>
                        <div class="info-value">{{ compartment_qr.created_date.strftime('%d-%m-%Y') }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Status</div>
                        <div class="info-value">{% if compartment_qr.is_active %}Active{% else %}Inactive{% endif %}</div>
                    </div>
                </div>
            </div>

            <div class="details-section">
                <h3 class="section-title">
                    <i class="fas fa-layer-group"></i>Bundle Range
                </h3>
                <p class="section-description">
                    This compartment contains bundles numbered from {{ compartment_qr.bundle_range_start }} to {{ compartment_qr.bundle_range_end }}.
                </p>

                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">Range Start</div>
                        <div class="info-value">{{ compartment_qr.bundle_range_start }}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Range End</div>
                        <div class="info-value">{{ compartment_qr.bundle_range_end }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bundle Navigation -->
    <div class="bundle-navigation-section">
        <h3 class="section-title">
            <i class="fas fa-list"></i>Bundle Navigation
        </h3>
        <p class="section-description">
            Click on any bundle number to view files in that bundle. Bundles {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }} are organized in this compartment.
        </p>

        <div class="bundle-grid">
            {% for bundle_num in compartment_qr.get_bundle_list() %}
            <a href="{{ url_for('view_compartment_bundle_files', compartment_number=compartment_qr.compartment_number, bundle_number=bundle_num) }}"
               class="bundle-item"
               title="View files in Bundle {{ bundle_num }}">
                {{ bundle_num }}
            </a>
            {% endfor %}
        </div>
    </div>

    <!-- Bundle QR Management -->
    <div class="bundle-qr-management">
        <h3 class="section-title">
            <i class="fas fa-qrcode"></i>Individual Bundle QR Codes
        </h3>
        <p class="section-description">
            Generate, download, and manage QR codes for individual bundles. Each bundle has its own unique QR code for physical file organization and tracking.
        </p>

        <div class="qr-actions-grid">
            <button onclick="generateAllBundleQRs()" class="action-btn primary">
                <i class="fas fa-magic"></i>Generate All QR Codes
            </button>
            <button onclick="downloadAllBundleQRs()" class="action-btn secondary">
                <i class="fas fa-download"></i>Download ZIP Archive
            </button>
            <button onclick="printBundleQRSheet()" class="action-btn secondary">
                <i class="fas fa-print"></i>Print QR Sheet
            </button>
        </div>

        <div class="bundle-qr-preview">
            {% for bundle_num in compartment_qr.get_bundle_list()[:6] %}
            <div class="bundle-qr-card" style="border: 1px solid var(--gov-border); border-radius: var(--gov-radius-lg); padding: 1rem; text-align: center; background: var(--gov-white); box-shadow: var(--gov-shadow-sm);">
                <div style="margin-bottom: 0.75rem;">
                    <h5 style="margin: 0; color: var(--gov-primary); font-weight: 700; font-size: 0.875rem;">{{ bundle_num }}</h5>
                </div>

                <div style="margin-bottom: 0.75rem;">
                    <img src="{{ url_for('static', filename='qrcodes/bundle_' + bundle_num|string + '_qr.png') }}"
                         alt="Bundle {{ bundle_num }} QR Code"
                         style="width: 80px; height: 80px; border: 1px solid var(--gov-border); border-radius: var(--gov-radius-md);"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display: none; width: 80px; height: 80px; border: 1px dashed var(--gov-border); border-radius: var(--gov-radius-md); display: flex; align-items: center; justify-content: center; color: var(--gov-gray-500); font-size: 0.625rem; margin: 0 auto; background: var(--gov-gray-50);">
                        Not Generated
                    </div>
                </div>

                <div style="display: flex; gap: 0.5rem; justify-content: center;">
                    <a href="{{ url_for('bundle_qr_code', bundle_number=bundle_num) }}"
                       class="action-btn secondary"
                       target="_blank"
                       title="Download QR Code">
                        <i class="fas fa-download"></i>
                    </a>
                    <a href="{{ url_for('view_compartment_bundle_files', compartment_number=compartment_qr.compartment_number, bundle_number=bundle_num) }}"
                       class="action-btn primary"
                       title="View Bundle Files">
                        <i class="fas fa-eye"></i>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if compartment_qr.get_bundle_list()|length > 6 %}
        <div style="text-align: center; margin-top: 1.5rem; padding-top: 1.5rem; border-top: 1px solid var(--gov-border);">
            <p style="color: var(--gov-gray-600); margin-bottom: 1rem; font-size: 0.875rem;">
                Showing first 6 bundle QR codes. <strong>{{ compartment_qr.get_bundle_list()|length - 6 }}</strong> more bundles available.
            </p>
            <button onclick="showAllBundleQRs()" class="action-btn secondary">
                <i class="fas fa-expand"></i>Show All {{ compartment_qr.get_bundle_list()|length }} Bundle QR Codes
            </button>
        </div>
        {% endif %}
    </div>

    <!-- QR Data Section -->
    <div class="qr-data-section">
        <h3 class="section-title">
            <i class="fas fa-code"></i>QR Code Data (JSON)
        </h3>
        <p class="section-description">
            Technical data encoded in the compartment QR code for system integration and verification.
        </p>

        <div class="qr-data-content">
            <div class="json-display" id="qr-data">{{ compartment_qr.qr_data }}</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function printQR() {
    const qrImage = document.querySelector('.qr-image-large');
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Compartment {{ compartment_qr.compartment_number }} QR Code</title>
                <style>
                    body { text-align: center; font-family: Arial, sans-serif; }
                    img { max-width: 400px; margin: 20px; }
                    h1 { color: #333; }
                </style>
            </head>
            <body>
                <h1>Compartment {{ compartment_qr.compartment_number }} QR Code</h1>
                <p>Bundles {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }}</p>
                <img src="${qrImage.src}" alt="QR Code">
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function copyQRData() {
    const qrData = document.getElementById('qr-data').textContent;
    navigator.clipboard.writeText(qrData).then(() => {
        // Show success message
        const btn = event.target.closest('.action-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>Copied!';
        btn.style.background = 'var(--success-color)';

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy QR data to clipboard');
    });
}

// Bundle QR Code Management Functions
function generateAllBundleQRs() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Generating...';
    btn.disabled = true;

    fetch('/api/generate-bundle-qrs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            compartment: {{ compartment_qr.compartment_number }}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            btn.innerHTML = '<i class="fas fa-check"></i>Generated!';
            btn.style.background = 'var(--success-color)';
            setTimeout(() => {
                location.reload(); // Reload to show new QR codes
            }, 1500);
        } else {
            throw new Error(data.message || 'Generation failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>Error';
        btn.style.background = 'var(--danger-color)';
        alert('Failed to generate bundle QR codes: ' + error.message);
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
            btn.disabled = false;
        }, 3000);
    });
}

function downloadAllBundleQRs() {
    window.open(`/api/download-bundle-qrs/{{ compartment_qr.compartment_number }}`, '_blank');
}

function printBundleQRSheet() {
    const compartmentNumber = {{ compartment_qr.compartment_number }};
    const bundleRange = [{{ compartment_qr.bundle_range_start }}, {{ compartment_qr.bundle_range_end }}];

    const printWindow = window.open('', '_blank');
    let printContent = `
        <html>
            <head>
                <title>Bundle QR Codes - Compartment ${compartmentNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .qr-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; }
                    .qr-item { text-align: center; border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
                    .qr-item img { width: 120px; height: 120px; }
                    .qr-item h4 { margin: 10px 0 5px 0; }
                    .qr-item p { margin: 0; font-size: 12px; color: #666; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Bundle QR Codes - Compartment ${compartmentNumber}</h1>
                    <p>Bundles ${bundleRange[0]}-${bundleRange[1]} • Generated on ${new Date().toLocaleDateString()}</p>
                </div>
                <div class="qr-grid">
    `;

    // Add first 20 bundles for printing
    const bundleCards = document.querySelectorAll('.bundle-qr-card');
    bundleCards.forEach(card => {
        const bundleNum = card.querySelector('h4').textContent;
        const qrImg = card.querySelector('img');
        if (qrImg && qrImg.style.display !== 'none') {
            printContent += `
                <div class="qr-item">
                    <img src="${qrImg.src}" alt="${bundleNum} QR Code">
                    <h4>${bundleNum}</h4>
                    <p>Compartment ${compartmentNumber}</p>
                </div>
            `;
        }
    });

    printContent += `
                </div>
            </body>
        </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function showAllBundleQRs() {
    // This would typically load more bundle QR codes via AJAX
    // For now, we'll redirect to a full view
    window.location.href = `{{ url_for('view_compartment_qr', compartment_number=compartment_qr.compartment_number) }}?show_all_qrs=true`;
}

// Format JSON display
document.addEventListener('DOMContentLoaded', function() {
    const jsonDisplay = document.getElementById('qr-data');
    try {
        const jsonData = JSON.parse(jsonDisplay.textContent);
        jsonDisplay.textContent = JSON.stringify(jsonData, null, 2);
    } catch (e) {
        console.log('JSON already formatted or invalid');
    }
});
</script>
{% endblock %}
