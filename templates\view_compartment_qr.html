{% extends "base.html" %}

{% block title %}Compartment {{ compartment_qr.compartment_number }} QR Code - Taluk Office{% endblock %}

{% block styles %}
<style>
.qr-detail-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.qr-detail-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.qr-display {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    padding: 2rem;
    text-align: center;
}

.qr-image-large {
    max-width: 100%;
    height: auto;
    border: 3px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    background: white;
    margin-bottom: 1.5rem;
}

.qr-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.qr-info {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.section-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

.section-subtitle {
    padding: 1rem 2rem;
    margin: 0;
    color: var(--gray-600);
    font-style: italic;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    padding: 2rem;
}

.info-card {
    text-align: center;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.info-icon {
    font-size: 2rem;
    margin-bottom: 0.75rem;
    color: var(--primary-color);
}

.info-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-800);
}

.bundle-list {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 2rem;
}

.bundle-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
    gap: 0.5rem;
    padding: 2rem;
    max-height: 400px;
    overflow-y: auto;
}

.bundle-item {
    background: var(--gray-100);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    padding: 0.5rem;
    text-align: center;
    font-weight: 600;
    color: var(--gray-700);
    transition: var(--transition-fast);
    text-decoration: none;
    display: block;
}

.bundle-item:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
    text-decoration: none;
}

.bundle-item.clickable {
    cursor: pointer;
}

.bundle-item.clickable:active {
    transform: scale(0.95);
}

.qr-data-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-top: 2rem;
}

.qr-data-content {
    padding: 2rem;
}

.json-display {
    background: var(--gray-900);
    color: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--radius-md);
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    overflow-x: auto;
    white-space: pre-wrap;
    line-height: 1.5;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
    cursor: pointer;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn.success {
    background: var(--gradient-success);
    color: white;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    color: inherit;
}

@media (max-width: 992px) {
    .qr-detail-grid {
        grid-template-columns: 1fr;
    }

    .qr-actions {
        flex-direction: column;
        align-items: center;
    }

    .bundle-grid {
        grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Header -->
    <div class="qr-detail-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="welcome-text">
                    <i class="fas fa-archive me-3"></i>Compartment {{ compartment_qr.compartment_number }} QR Code
                </h1>
                <p class="welcome-subtitle">
                    Bundles {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }}
                    ({{ compartment_qr.get_bundle_list()|length }} total bundles)
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{{ url_for('compartment_qr_management') }}" class="quick-action-btn">
                    <i class="fas fa-arrow-left"></i>Back to QR Codes
                </a>
            </div>
        </div>
    </div>

    <!-- QR Code Display and Info -->
    <div class="qr-detail-grid">
        <!-- QR Code Display -->
        <div class="qr-display">
            <img src="{{ url_for('get_compartment_qr_image', compartment_number=compartment_qr.compartment_number) }}"
                 alt="Compartment {{ compartment_qr.compartment_number }} QR Code"
                 class="qr-image-large">

            <div class="qr-actions">
                <a href="{{ url_for('download_compartment_qr', compartment_number=compartment_qr.compartment_number) }}"
                   class="action-btn primary">
                    <i class="fas fa-download"></i>Download PNG
                </a>
                <button onclick="printQR()" class="action-btn secondary">
                    <i class="fas fa-print"></i>Print QR Code
                </button>
                <button onclick="copyQRData()" class="action-btn success">
                    <i class="fas fa-copy"></i>Copy QR Data
                </button>
            </div>
        </div>

        <!-- QR Info -->
        <div class="qr-info">
            <h2 class="section-header">
                <i class="fas fa-info-circle me-2"></i>Compartment Information
            </h2>

            <div class="info-grid">
                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="info-label">Compartment</div>
                    <div class="info-value">{{ compartment_qr.compartment_number }}</div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-boxes"></i>
                    </div>
                    <div class="info-label">Total Bundles</div>
                    <div class="info-value">{{ compartment_qr.get_bundle_list()|length }}</div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-calendar"></i>
                    </div>
                    <div class="info-label">Created</div>
                    <div class="info-value">{{ compartment_qr.created_date.strftime('%Y-%m-%d') }}</div>
                </div>

                <div class="info-card">
                    <div class="info-icon">
                        <i class="fas fa-toggle-{{ 'on' if compartment_qr.is_active else 'off' }}"></i>
                    </div>
                    <div class="info-label">Status</div>
                    <div class="info-value">{{ 'Active' if compartment_qr.is_active else 'Inactive' }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bundle List -->
    <div class="bundle-list">
        <h2 class="section-header">
            <i class="fas fa-list me-2"></i>Bundle Numbers ({{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }})
        </h2>
        <p class="section-subtitle">Click on any bundle number to view files in that bundle</p>

        <div class="bundle-grid">
            {% for bundle_num in compartment_qr.get_bundle_list() %}
            <a href="{{ url_for('view_compartment_bundle_files', compartment_number=compartment_qr.compartment_number, bundle_number=bundle_num) }}"
               class="bundle-item clickable"
               title="View files in Bundle {{ bundle_num }}">
                {{ bundle_num }}
            </a>
            {% endfor %}
        </div>
    </div>

    <!-- Bundle QR Codes Section -->
    <div class="bundle-qr-section" style="margin-top: 3rem;">
        <h2 class="section-header">
            <i class="fas fa-qrcode me-2"></i>Individual Bundle QR Codes
        </h2>
        <p class="section-subtitle">Each bundle has its own unique QR code for physical file organization</p>

        <div class="bundle-qr-actions" style="margin-bottom: 2rem; display: flex; gap: 1rem; flex-wrap: wrap;">
            <button onclick="generateAllBundleQRs()" class="action-btn primary">
                <i class="fas fa-magic"></i>Generate All Bundle QR Codes
            </button>
            <button onclick="downloadAllBundleQRs()" class="action-btn secondary">
                <i class="fas fa-download"></i>Download All QR Codes (ZIP)
            </button>
            <button onclick="printBundleQRSheet()" class="action-btn secondary">
                <i class="fas fa-print"></i>Print QR Code Sheet
            </button>
        </div>

        <div class="bundle-qr-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 1rem;">
            {% for bundle_num in compartment_qr.get_bundle_list()[:20] %}
            <div class="bundle-qr-card" style="border: 1px solid var(--border-color); border-radius: var(--radius-lg); padding: 1rem; text-align: center; background: var(--white);">
                <div class="bundle-qr-header" style="margin-bottom: 1rem;">
                    <h4 style="margin: 0; color: var(--primary-color);">Bundle {{ bundle_num }}</h4>
                    <small style="color: var(--gray-600);">Compartment {{ compartment_qr.compartment_number }}</small>
                </div>

                <div class="bundle-qr-image" style="margin-bottom: 1rem;">
                    <img src="{{ url_for('static', filename='qrcodes/bundle_' + bundle_num|string + '_qr.png') }}"
                         alt="Bundle {{ bundle_num }} QR Code"
                         style="width: 100px; height: 100px; border: 1px solid var(--gray-300); border-radius: var(--radius-md);"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                    <div style="display: none; width: 100px; height: 100px; border: 1px dashed var(--gray-300); border-radius: var(--radius-md); display: flex; align-items: center; justify-content: center; color: var(--gray-500); font-size: 0.75rem; margin: 0 auto;">
                        QR Not Generated
                    </div>
                </div>

                <div class="bundle-qr-actions" style="display: flex; gap: 0.5rem; justify-content: center;">
                    <a href="{{ url_for('bundle_qr_code', bundle_number=bundle_num) }}"
                       class="action-btn secondary"
                       style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                       target="_blank">
                        <i class="fas fa-download"></i>
                    </a>
                    <a href="{{ url_for('view_compartment_bundle_files', compartment_number=compartment_qr.compartment_number, bundle_number=bundle_num) }}"
                       class="action-btn primary"
                       style="font-size: 0.75rem; padding: 0.25rem 0.5rem;">
                        <i class="fas fa-eye"></i>
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if compartment_qr.get_bundle_list()|length > 20 %}
        <div style="text-align: center; margin-top: 2rem;">
            <p style="color: var(--gray-600); margin-bottom: 1rem;">
                Showing first 20 bundles. <strong>{{ compartment_qr.get_bundle_list()|length - 20 }}</strong> more bundles available.
            </p>
            <button onclick="showAllBundleQRs()" class="action-btn secondary">
                <i class="fas fa-expand"></i>Show All {{ compartment_qr.get_bundle_list()|length }} Bundle QR Codes
            </button>
        </div>
        {% endif %}
    </div>

    <!-- QR Data -->
    <div class="qr-data-section">
        <h2 class="section-header">
            <i class="fas fa-code me-2"></i>QR Code Data (JSON)
        </h2>

        <div class="qr-data-content">
            <div class="json-display" id="qr-data">{{ compartment_qr.qr_data }}</div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function printQR() {
    const qrImage = document.querySelector('.qr-image-large');
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>Compartment {{ compartment_qr.compartment_number }} QR Code</title>
                <style>
                    body { text-align: center; font-family: Arial, sans-serif; }
                    img { max-width: 400px; margin: 20px; }
                    h1 { color: #333; }
                </style>
            </head>
            <body>
                <h1>Compartment {{ compartment_qr.compartment_number }} QR Code</h1>
                <p>Bundles {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }}</p>
                <img src="${qrImage.src}" alt="QR Code">
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

function copyQRData() {
    const qrData = document.getElementById('qr-data').textContent;
    navigator.clipboard.writeText(qrData).then(() => {
        // Show success message
        const btn = event.target.closest('.action-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>Copied!';
        btn.style.background = 'var(--success-color)';

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy: ', err);
        alert('Failed to copy QR data to clipboard');
    });
}

// Bundle QR Code Management Functions
function generateAllBundleQRs() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>Generating...';
    btn.disabled = true;

    fetch('/api/generate-bundle-qrs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            compartment: {{ compartment_qr.compartment_number }}
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            btn.innerHTML = '<i class="fas fa-check"></i>Generated!';
            btn.style.background = 'var(--success-color)';
            setTimeout(() => {
                location.reload(); // Reload to show new QR codes
            }, 1500);
        } else {
            throw new Error(data.message || 'Generation failed');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i>Error';
        btn.style.background = 'var(--danger-color)';
        alert('Failed to generate bundle QR codes: ' + error.message);
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
            btn.disabled = false;
        }, 3000);
    });
}

function downloadAllBundleQRs() {
    window.open(`/api/download-bundle-qrs/{{ compartment_qr.compartment_number }}`, '_blank');
}

function printBundleQRSheet() {
    const compartmentNumber = {{ compartment_qr.compartment_number }};
    const bundleRange = [{{ compartment_qr.bundle_range_start }}, {{ compartment_qr.bundle_range_end }}];

    const printWindow = window.open('', '_blank');
    let printContent = `
        <html>
            <head>
                <title>Bundle QR Codes - Compartment ${compartmentNumber}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    .header { text-align: center; margin-bottom: 30px; }
                    .qr-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; }
                    .qr-item { text-align: center; border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
                    .qr-item img { width: 120px; height: 120px; }
                    .qr-item h4 { margin: 10px 0 5px 0; }
                    .qr-item p { margin: 0; font-size: 12px; color: #666; }
                    @media print { .no-print { display: none; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Bundle QR Codes - Compartment ${compartmentNumber}</h1>
                    <p>Bundles ${bundleRange[0]}-${bundleRange[1]} • Generated on ${new Date().toLocaleDateString()}</p>
                </div>
                <div class="qr-grid">
    `;

    // Add first 20 bundles for printing
    const bundleCards = document.querySelectorAll('.bundle-qr-card');
    bundleCards.forEach(card => {
        const bundleNum = card.querySelector('h4').textContent;
        const qrImg = card.querySelector('img');
        if (qrImg && qrImg.style.display !== 'none') {
            printContent += `
                <div class="qr-item">
                    <img src="${qrImg.src}" alt="${bundleNum} QR Code">
                    <h4>${bundleNum}</h4>
                    <p>Compartment ${compartmentNumber}</p>
                </div>
            `;
        }
    });

    printContent += `
                </div>
            </body>
        </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function showAllBundleQRs() {
    // This would typically load more bundle QR codes via AJAX
    // For now, we'll redirect to a full view
    window.location.href = `{{ url_for('view_compartment_qr', compartment_number=compartment_qr.compartment_number) }}?show_all_qrs=true`;
}

// Format JSON display
document.addEventListener('DOMContentLoaded', function() {
    const jsonDisplay = document.getElementById('qr-data');
    try {
        const jsonData = JSON.parse(jsonDisplay.textContent);
        jsonDisplay.textContent = JSON.stringify(jsonData, null, 2);
    } catch (e) {
        console.log('JSON already formatted or invalid');
    }
});
</script>
{% endblock %}
