# Taluk Office Management System - Environment Configuration

# Flask Configuration
SECRET_KEY=your-secret-key-here-change-this-in-production
DEBUG=False
HOST=127.0.0.1
PORT=5000

# Database Configuration
# For SQLite (default)
DATABASE_URL=sqlite:///toffice.db

# For MySQL (uncomment and configure if using MySQL)
# DATABASE_URL=mysql://username:password@localhost/toffice_db

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# QR Code Configuration
QR_CODE_SIZE=10
QR_CODE_BORDER=4

# Application Settings
FILES_PER_PAGE=20
UPLOAD_FOLDER=static/uploads
QR_CODE_FOLDER=static/qrcodes
