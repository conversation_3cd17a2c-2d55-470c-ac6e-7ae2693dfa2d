<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bundle Management - T-Office</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .bundle-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .bundle-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid #007bff;
        }

        .bundle-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .bundle-card.empty {
            border-left-color: #6c757d;
            background: #f8f9fa;
        }

        .bundle-card.active {
            border-left-color: #28a745;
        }

        .bundle-card.nearly-full {
            border-left-color: #ffc107;
        }

        .bundle-card.full {
            border-left-color: #dc3545;
        }

        .bundle-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .bundle-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }

        .bundle-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
        }

        .status-empty {
            background: #e9ecef;
            color: #6c757d;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-nearly-full {
            background: #fff3cd;
            color: #856404;
        }

        .status-full {
            background: #f8d7da;
            color: #721c24;
        }

        .capacity-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 0.5rem 0;
        }

        .capacity-fill {
            height: 100%;
            transition: width 0.3s ease;
        }

        .capacity-fill.empty {
            background: #6c757d;
        }

        .capacity-fill.active {
            background: #28a745;
        }

        .capacity-fill.nearly-full {
            background: #ffc107;
        }

        .capacity-fill.full {
            background: #dc3545;
        }

        .bundle-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .action-btn {
            flex: 1;
            padding: 0.5rem;
            border: none;
            border-radius: 6px;
            font-size: 0.85rem;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: #007bff;
            color: white;
        }

        .action-btn.primary:hover {
            background: #0056b3;
            color: white;
        }

        .action-btn.secondary {
            background: #6c757d;
            color: white;
        }

        .action-btn.secondary:hover {
            background: #545b62;
            color: white;
        }

        .stats-overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .compartment-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .compartment-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }

        .compartment-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
            color: #007bff;
        }

        .admin-actions {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .admin-btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-archive me-2"></i>T-Office Bundle Management
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('compartment_search') }}">
                    <i class="fas fa-search me-1"></i>Search
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Statistics Overview -->
        <div class="stats-overview">
            <h2 class="mb-3">
                <i class="fas fa-chart-bar me-2"></i>Bundle Overview
            </h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.total_bundles }}</div>
                    <div class="stat-label">Total Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.total_files }}</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.active_bundles }}</div>
                    <div class="stat-label">Active Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ "%.1f"|format(bundle_stats.avg_capacity_usage) }}%</div>
                    <div class="stat-label">Avg Capacity</div>
                </div>
            </div>
        </div>

        <!-- Admin Actions -->
        {% if current_user.role == 'Administrator' %}
        <div class="admin-actions">
            <h5><i class="fas fa-tools me-2"></i>Administrative Actions</h5>
            <a href="{{ url_for('initialize_bundles') }}" class="btn btn-primary admin-btn">
                <i class="fas fa-plus me-1"></i>Initialize Bundles
            </a>
            <a href="{{ url_for('update_bundle_counts') }}" class="btn btn-success admin-btn">
                <i class="fas fa-sync me-1"></i>Update Counts
            </a>
            <a href="{{ url_for('generate_all_bundle_qr_codes') }}" class="btn btn-info admin-btn">
                <i class="fas fa-qrcode me-1"></i>Generate QR Codes
            </a>
            <a href="{{ url_for('bundle_statistics') }}" class="btn btn-warning admin-btn">
                <i class="fas fa-chart-line me-1"></i>Detailed Statistics
            </a>
            <a href="{{ url_for('fix_compartment_assignments') }}" class="btn btn-secondary admin-btn">
                <i class="fas fa-tools me-1"></i>Fix Compartment Assignments
            </a>
        </div>
        {% endif %}

        <!-- Bundle Grid -->
        <div class="bundle-grid">
            {% for bundle in bundles %}
            <div class="bundle-card {{ bundle.get_status() }}">
                <div class="bundle-header">
                    <div class="bundle-number">Bundle {{ bundle.bundle_number }}</div>
                    <span class="bundle-status status-{{ bundle.get_status() }}">
                        {{ bundle.get_status().replace('_', ' ') }}
                    </span>
                </div>

                <div class="bundle-info">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Files:</span>
                        <strong>{{ bundle.current_count }}/{{ bundle.max_capacity }}</strong>
                    </div>

                    <div class="capacity-bar">
                        <div class="capacity-fill {{ bundle.get_status() }}"
                             style="width: {{ bundle.get_capacity_percentage() }}%"></div>
                    </div>

                    <div class="small text-muted">
                        {% if bundle.get_compartment_info() %}
                        <i class="fas fa-box me-1"></i>
                        Compartment {{ bundle.get_compartment_info().compartment_number }}
                        {% else %}
                        <i class="fas fa-question-circle me-1"></i>
                        No compartment assigned
                        {% endif %}
                    </div>
                </div>

                <div class="bundle-actions">
                    <a href="{{ url_for('bundle_detail', bundle_number=bundle.bundle_number) }}"
                       class="action-btn primary">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                    {% if bundle.qr_code_path %}
                    <a href="{{ url_for('bundle_qr_code', bundle_number=bundle.bundle_number) }}"
                       class="action-btn secondary" target="_blank">
                        <i class="fas fa-qrcode me-1"></i>QR
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Compartment Distribution -->
        {% if compartment_distribution %}
        <div class="compartment-section">
            <div class="compartment-header">
                <i class="fas fa-warehouse compartment-icon"></i>
                <h3>Compartment Distribution</h3>
            </div>

            <div class="row">
                {% for comp_num, comp_data in compartment_distribution.items() %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card {% if not comp_data.is_active %}border-secondary{% endif %}">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-box me-1"></i>
                                Compartment {{ comp_data.compartment_number }}
                                {% if comp_data.is_active %}
                                <span class="badge bg-success ms-2">Active</span>
                                {% else %}
                                <span class="badge bg-secondary ms-2">Available</span>
                                {% endif %}
                            </h6>
                            <p class="card-text">
                                <small class="text-muted">{{ comp_data.bundle_range }}</small><br>
                                {% if comp_data.is_active %}
                                <strong>{{ comp_data.total_bundles }}</strong> bundles assigned<br>
                                <strong>{{ comp_data.total_files }}</strong> files stored
                                {% else %}
                                <span class="text-muted">No bundles currently assigned</span><br>
                                <small class="text-muted">Reserved for future expansion</small>
                                {% endif %}
                            </p>
                            {% if comp_data.original_range %}
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                QR Range: {{ comp_data.original_range }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh bundle counts every 5 minutes
        setInterval(function() {
            // Only refresh if user is Administrator
            {% if current_user.role == 'Administrator' %}
            fetch('/admin/bundles/update-counts')
                .then(() => {
                    console.log('Bundle counts updated');
                })
                .catch(error => {
                    console.error('Error updating bundle counts:', error);
                });
            {% endif %}
        }, 300000); // 5 minutes

        // Add loading states to admin buttons
        document.querySelectorAll('.admin-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
                this.disabled = true;
            });
        });
    </script>
</body>
</html>
