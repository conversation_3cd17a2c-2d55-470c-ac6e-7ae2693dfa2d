{% extends "base.html" %}

{% block title %}Bundle Management - Taluk Office{% endblock %}

{% block styles %}
<style>
        /* Official Government Bundle Management Styling */
        :root {
            --bundle-primary: #003366;
            --bundle-secondary: #002244;
            --bundle-accent: #FFD700;
            --bundle-success: #006600;
            --bundle-white: #ffffff;
            --bundle-gray-50: #f8fafc;
            --bundle-gray-100: #f1f5f9;
            --bundle-gray-200: #e2e8f0;
            --bundle-gray-600: #475569;
            --bundle-gray-700: #334155;
            --bundle-gray-800: #1e293b;
            --bundle-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --bundle-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --bundle-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --bundle-radius-lg: 0.5rem;
            --bundle-radius-xl: 0.75rem;
            --bundle-transition: all 0.3s ease;
        }

        .bundle-header {
            background: linear-gradient(135deg, var(--bundle-primary) 0%, var(--bundle-secondary) 100%);
            color: var(--bundle-white);
            padding: 2.5rem 2rem;
            border-radius: var(--bundle-radius-xl);
            margin-bottom: 2rem;
            box-shadow: var(--bundle-shadow-lg);
            position: relative;
            overflow: hidden;
        }

        .bundle-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--bundle-accent), var(--bundle-success), var(--bundle-accent));
        }

        .bundle-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .bundle-card {
            background: var(--bundle-white);
            border-radius: var(--bundle-radius-xl);
            padding: 1.5rem;
            box-shadow: var(--bundle-shadow-md);
            transition: var(--bundle-transition);
            border: 1px solid var(--bundle-gray-200);
            border-left: 4px solid var(--bundle-primary);
        }

        .bundle-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--bundle-shadow-lg);
            border-left-color: var(--bundle-accent);
        }

        .bundle-card.empty {
            border-left-color: var(--bundle-gray-600);
            background: var(--bundle-gray-50);
        }

        .bundle-card.active {
            border-left-color: var(--bundle-success);
        }

        .bundle-card.nearly-full {
            border-left-color: var(--bundle-accent);
        }

        .bundle-card.full {
            border-left-color: #dc3545;
        }

        .bundle-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .bundle-number {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--bundle-primary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .bundle-status {
            padding: 0.25rem 0.75rem;
            border-radius: var(--bundle-radius-lg);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-empty {
            background: var(--bundle-gray-200);
            color: var(--bundle-gray-600);
        }

        .status-active {
            background: rgba(0, 102, 0, 0.1);
            color: var(--bundle-success);
        }

        .status-nearly-full {
            background: rgba(255, 215, 0, 0.2);
            color: #b8860b;
        }

        .status-full {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .capacity-bar {
            width: 100%;
            height: 10px;
            background: var(--bundle-gray-200);
            border-radius: var(--bundle-radius-lg);
            overflow: hidden;
            margin: 0.75rem 0;
            border: 1px solid var(--bundle-gray-300);
        }

        .capacity-fill {
            height: 100%;
            transition: var(--bundle-transition);
            border-radius: var(--bundle-radius-lg);
        }

        .capacity-fill.empty {
            background: var(--bundle-gray-600);
        }

        .capacity-fill.active {
            background: var(--bundle-success);
        }

        .capacity-fill.nearly-full {
            background: var(--bundle-accent);
        }

        .capacity-fill.full {
            background: #dc3545;
        }

        .bundle-actions {
            display: flex;
            gap: 0.75rem;
            margin-top: 1.5rem;
        }

        .action-btn {
            flex: 1;
            min-width: 100px;
            height: 40px;
            padding: 0 1rem;
            border: none;
            border-radius: var(--bundle-radius-lg);
            font-size: 0.875rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            transition: var(--bundle-transition);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            box-shadow: var(--bundle-shadow-sm);
            border: 2px solid transparent;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--bundle-primary), var(--bundle-secondary));
            color: var(--bundle-white);
            border-color: var(--bundle-primary);
        }

        .action-btn.primary:hover {
            background: var(--bundle-secondary);
            border-color: var(--bundle-secondary);
            color: var(--bundle-white);
            transform: translateY(-1px);
            box-shadow: var(--bundle-shadow-md);
            text-decoration: none;
        }

        .action-btn.secondary {
            background: var(--bundle-white);
            color: var(--bundle-gray-700);
            border-color: var(--bundle-gray-200);
        }

        .action-btn.secondary:hover {
            background: var(--bundle-gray-50);
            border-color: var(--bundle-primary);
            color: var(--bundle-primary);
            transform: translateY(-1px);
            box-shadow: var(--bundle-shadow-md);
            text-decoration: none;
        }

        .stats-overview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .compartment-section {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .compartment-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f8f9fa;
        }

        .compartment-icon {
            font-size: 1.5rem;
            margin-right: 0.5rem;
            color: #007bff;
        }

        .admin-actions {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .admin-btn {
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-archive me-2"></i>T-Office Bundle Management
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard') }}">
                    <i class="fas fa-home me-1"></i>Dashboard
                </a>
                <a class="nav-link" href="{{ url_for('compartment_search') }}">
                    <i class="fas fa-search me-1"></i>Search
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Statistics Overview -->
        <div class="stats-overview">
            <h2 class="mb-3">
                <i class="fas fa-chart-bar me-2"></i>Bundle Overview
            </h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.total_bundles }}</div>
                    <div class="stat-label">Total Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.total_files }}</div>
                    <div class="stat-label">Total Files</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ bundle_stats.active_bundles }}</div>
                    <div class="stat-label">Active Bundles</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ "%.1f"|format(bundle_stats.avg_capacity_usage) }}%</div>
                    <div class="stat-label">Avg Capacity</div>
                </div>
            </div>
        </div>

        <!-- Admin Actions -->
        {% if current_user.role == 'Administrator' %}
        <div class="admin-actions">
            <h5><i class="fas fa-tools me-2"></i>Administrative Actions</h5>
            <a href="{{ url_for('initialize_bundles') }}" class="btn btn-primary admin-btn">
                <i class="fas fa-plus me-1"></i>Initialize Bundles
            </a>
            <a href="{{ url_for('update_bundle_counts') }}" class="btn btn-success admin-btn">
                <i class="fas fa-sync me-1"></i>Update Counts
            </a>
            <a href="{{ url_for('generate_all_bundle_qr_codes') }}" class="btn btn-info admin-btn">
                <i class="fas fa-qrcode me-1"></i>Generate QR Codes
            </a>
            <a href="{{ url_for('bundle_statistics') }}" class="btn btn-warning admin-btn">
                <i class="fas fa-chart-line me-1"></i>Detailed Statistics
            </a>
            <a href="{{ url_for('fix_compartment_assignments') }}" class="btn btn-secondary admin-btn">
                <i class="fas fa-tools me-1"></i>Fix Compartment Assignments
            </a>
        </div>
        {% endif %}

        <!-- Bundle Grid -->
        <div class="bundle-grid">
            {% for bundle in bundles %}
            <div class="bundle-card {{ bundle.get_status() }}">
                <div class="bundle-header">
                    <div class="bundle-number">Bundle {{ bundle.bundle_number }}</div>
                    <span class="bundle-status status-{{ bundle.get_status() }}">
                        {{ bundle.get_status().replace('_', ' ') }}
                    </span>
                </div>

                <div class="bundle-info">
                    <div class="d-flex justify-content-between mb-2">
                        <span>Files:</span>
                        <strong>{{ bundle.current_count }}/{{ bundle.max_capacity }}</strong>
                    </div>

                    <div class="capacity-bar">
                        <div class="capacity-fill {{ bundle.get_status() }}"
                             style="width: {{ bundle.get_capacity_percentage() }}%"></div>
                    </div>

                    <div class="small text-muted">
                        {% if bundle.get_compartment_info() %}
                        <i class="fas fa-box me-1"></i>
                        Compartment {{ bundle.get_compartment_info().compartment_number }}
                        {% else %}
                        <i class="fas fa-question-circle me-1"></i>
                        No compartment assigned
                        {% endif %}
                    </div>
                </div>

                <div class="bundle-actions">
                    <a href="{{ url_for('bundle_detail', bundle_number=bundle.bundle_number) }}"
                       class="action-btn primary">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                    {% if bundle.qr_code_path %}
                    <a href="{{ url_for('bundle_qr_code', bundle_number=bundle.bundle_number) }}"
                       class="action-btn secondary" target="_blank">
                        <i class="fas fa-qrcode me-1"></i>QR
                    </a>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Compartment Distribution -->
        {% if compartment_distribution %}
        <div class="compartment-section">
            <div class="compartment-header">
                <i class="fas fa-warehouse compartment-icon"></i>
                <h3>Compartment Distribution</h3>
            </div>

            <div class="row">
                {% for comp_num, comp_data in compartment_distribution.items() %}
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card {% if not comp_data.is_active %}border-secondary{% endif %}">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-box me-1"></i>
                                Compartment {{ comp_data.compartment_number }}
                                {% if comp_data.is_active %}
                                <span class="badge bg-success ms-2">Active</span>
                                {% else %}
                                <span class="badge bg-secondary ms-2">Available</span>
                                {% endif %}
                            </h6>
                            <p class="card-text">
                                <small class="text-muted">{{ comp_data.bundle_range }}</small><br>
                                {% if comp_data.is_active %}
                                <strong>{{ comp_data.total_bundles }}</strong> bundles assigned<br>
                                <strong>{{ comp_data.total_files }}</strong> files stored
                                {% else %}
                                <span class="text-muted">No bundles currently assigned</span><br>
                                <small class="text-muted">Reserved for future expansion</small>
                                {% endif %}
                            </p>
                            {% if comp_data.original_range %}
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                QR Range: {{ comp_data.original_range }}
                            </small>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh bundle counts every 5 minutes
        setInterval(function() {
            // Only refresh if user is Administrator
            {% if current_user.role == 'Administrator' %}
            fetch('/admin/bundles/update-counts')
                .then(() => {
                    console.log('Bundle counts updated');
                })
                .catch(error => {
                    console.error('Error updating bundle counts:', error);
                });
            {% endif %}
        }, 300000); // 5 minutes

        // Add loading states to admin buttons
        document.querySelectorAll('.admin-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
                this.disabled = true;
            });
        });
    </script>
</body>
</html>
