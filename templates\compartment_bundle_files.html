{% extends "base.html" %}

{% block title %}Bundle {{ bundle_number }} Files - Compartment {{ compartment_qr.compartment_number }}{% endblock %}

{% block styles %}
<style>
:root {
    --primary-color: #2563eb;
    --primary-light: #dbeafe;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
    --border-color: var(--gray-200);
    --text-color: var(--gray-800);
    --card-background: var(--white);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    --gradient-success: linear-gradient(135deg, var(--success-color), #047857);
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --transition-fast: all 0.15s ease;
    --transition-normal: all 0.3s ease;
}

.bundle-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem;
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    margin-bottom: 0;
}

.bundle-title {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bundle-subtitle {
    font-size: 1.1rem;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
}

.navigation-breadcrumb {
    background: var(--gray-50);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.breadcrumb-link {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.breadcrumb-link:hover {
    text-decoration: underline;
}

.bundle-stats {
    background: var(--white);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--gray-600);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.files-container {
    background: var(--white);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.files-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.file-item {
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    transition: var(--transition-fast);
}

.file-item:hover {
    background: var(--gray-50);
}

.file-item:last-child {
    border-bottom: none;
}

.file-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.file-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    flex: 1;
    margin-right: 1rem;
}

.file-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition-normal);
    cursor: pointer;
    font-size: 0.875rem;
}

.action-btn.primary {
    background: var(--gradient-primary);
    color: white;
}

.action-btn.secondary {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    text-decoration: none;
    color: inherit;
}

.file-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-group {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.detail-value {
    font-size: 0.875rem;
    color: var(--text-color);
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--gray-600);
}

.empty-icon {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: 1rem;
}

.back-button {
    background: var(--gray-100);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    transition: var(--transition-normal);
    margin-bottom: 2rem;
}

.back-button:hover {
    background: var(--gray-200);
    text-decoration: none;
    color: var(--gray-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

@media (max-width: 768px) {
    .bundle-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .file-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .file-details {
        grid-template-columns: 1fr;
    }

    .bundle-title {
        font-size: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Back Button -->
    <a href="{{ url_for('view_compartment_qr', compartment_number=compartment_qr.compartment_number) }}" class="back-button">
        <i class="fas fa-arrow-left"></i>Back to Compartment {{ compartment_qr.compartment_number }}
    </a>

    <!-- Bundle Files Container -->
    <div class="files-container">
        <!-- Bundle Header -->
        <div class="bundle-header">
            <div style="display: flex; justify-content: between; align-items: flex-start;">
                <div>
                    <h1 class="bundle-title">
                        <i class="fas fa-layer-group"></i>
                        Bundle {{ bundle_number }} Files
                    </h1>
                    <p class="bundle-subtitle">
                        Compartment {{ compartment_qr.compartment_number }} •
                        Bundle Range: {{ compartment_qr.bundle_range_start }}-{{ compartment_qr.bundle_range_end }}
                    </p>
                </div>
                <div style="display: flex; gap: 0.5rem; flex-shrink: 0;">
                    {% if bundle_record %}
                    <a href="{{ url_for('bundle_detail', bundle_number=bundle_number) }}"
                       class="action-btn secondary"
                       style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);"
                       title="View Bundle Management">
                        <i class="fas fa-cog"></i>Manage
                    </a>
                    {% endif %}
                    <a href="{{ url_for('compartment_bundles', compartment_number=compartment_qr.compartment_number) }}"
                       class="action-btn secondary"
                       style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3);"
                       title="View All Files in Compartment">
                        <i class="fas fa-list"></i>All Files
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Breadcrumb -->
        <div class="navigation-breadcrumb">
            <a href="{{ url_for('compartment_qr_management') }}" class="breadcrumb-link">Compartment QR</a>
            <span class="mx-2">›</span>
            <a href="{{ url_for('view_compartment_qr', compartment_number=compartment_qr.compartment_number) }}" class="breadcrumb-link">
                Compartment {{ compartment_qr.compartment_number }}
            </a>
            <span class="mx-2">›</span>
            <span>Bundle {{ bundle_number }}</span>
        </div>

        <!-- Bundle Statistics -->
        <div class="bundle-stats">
            <div class="stat-item">
                <div class="stat-value">{{ total_files }}</div>
                <div class="stat-label">Total Files</div>
            </div>
            {% if bundle_record %}
            <div class="stat-item">
                <div class="stat-value">{{ bundle_record.max_capacity }}</div>
                <div class="stat-label">Max Capacity</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ ((total_files / bundle_record.max_capacity) * 100) | round(1) }}%</div>
                <div class="stat-label">Utilization</div>
            </div>
            {% endif %}
            <div class="stat-item">
                <div class="stat-value">{{ compartment_qr.compartment_number }}</div>
                <div class="stat-label">Compartment</div>
            </div>
            {% if bundle_record and bundle_record.qr_code_path %}
            <div class="stat-item">
                <div style="display: flex; flex-direction: column; align-items: center; gap: 0.5rem;">
                    <img src="{{ url_for('static', filename='qrcodes/' + bundle_record.qr_code_path) }}"
                         alt="Bundle {{ bundle_number }} QR Code"
                         style="width: 80px; height: 80px; border: 2px solid var(--gray-300); border-radius: var(--radius-md);">
                    <div class="stat-label">Bundle QR Code</div>
                    <a href="{{ url_for('bundle_qr_code', bundle_number=bundle_number) }}"
                       class="action-btn secondary"
                       style="font-size: 0.75rem; padding: 0.25rem 0.5rem;"
                       target="_blank">
                        <i class="fas fa-download"></i>Download
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Search within Bundle (if files exist) -->
        {% if bundle_files %}
        <div class="bundle-search" style="padding: 1.5rem 2rem; background: var(--gray-50); border-bottom: 1px solid var(--border-color);">
            <div class="search-container" style="max-width: 500px;">
                <label for="bundle-file-search" style="display: block; font-weight: 600; margin-bottom: 0.5rem; color: var(--gray-700);">
                    <i class="fas fa-search me-2"></i>Search Files in Bundle {{ bundle_number }}
                </label>
                <input type="text"
                       id="bundle-file-search"
                       placeholder="Search by RefID, File Number, Category, Subject..."
                       style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-color); border-radius: var(--radius-md); font-size: 0.875rem;">
            </div>
        </div>
        {% endif %}

        <!-- Files List -->
        {% if bundle_files %}
        <ul class="files-list">
            {% for file_data in bundle_files %}
            <li class="file-item">
                <div class="file-header">
                    <h3 class="file-title">{{ file_data.file.title }}</h3>
                    <div class="file-actions">
                        <a href="{{ url_for('view_file', file_id=file_data.file.id) }}" class="action-btn primary">
                            <i class="fas fa-eye"></i>View
                        </a>
                        {% if file_data.file.qr_code %}
                        <a href="{{ url_for('get_qrcode', file_id=file_data.file.id) }}" class="action-btn secondary" target="_blank">
                            <i class="fas fa-qrcode"></i>QR Code
                        </a>
                        {% endif %}
                    </div>
                </div>

                <div class="file-details">
                    {% if file_data.excel_data.get('RefID') %}
                    <div class="detail-group">
                        <div class="detail-label">Reference ID</div>
                        <div class="detail-value">{{ file_data.excel_data.get('RefID') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('FILE_NO') %}
                    <div class="detail-group">
                        <div class="detail-label">File Number</div>
                        <div class="detail-value">{{ file_data.excel_data.get('FILE_NO') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('Category') %}
                    <div class="detail-group">
                        <div class="detail-label">Category</div>
                        <div class="detail-value">{{ file_data.excel_data.get('Category') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('hobli_name') %}
                    <div class="detail-group">
                        <div class="detail-label">Hobli</div>
                        <div class="detail-value">{{ file_data.excel_data.get('hobli_name') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('village_name') %}
                    <div class="detail-group">
                        <div class="detail-label">Village</div>
                        <div class="detail-value">{{ file_data.excel_data.get('village_name') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.excel_data.get('survey_no') %}
                    <div class="detail-group">
                        <div class="detail-label">Survey Number</div>
                        <div class="detail-value">{{ file_data.excel_data.get('survey_no') }}</div>
                    </div>
                    {% endif %}
                    {% if file_data.location %}
                    <div class="detail-group">
                        <div class="detail-label">Physical Location</div>
                        <div class="detail-value">Rack {{ file_data.location.rack_number }}, Row {{ file_data.location.row_number }}, Position {{ file_data.location.position }}</div>
                    </div>
                    {% endif %}
                    <div class="detail-group">
                        <div class="detail-label">Created Date</div>
                        <div class="detail-value">{{ file_data.file.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                    </div>
                </div>
            </li>
            {% endfor %}
        </ul>
        {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-folder-open"></i>
            </div>
            <h4>No Files in Bundle {{ bundle_number }}</h4>
            <p>This bundle doesn't contain any files yet. Files will appear here when they are uploaded with Bundle Number {{ bundle_number }}.</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Search functionality for files within bundle
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('bundle-file-search');
    const fileItems = document.querySelectorAll('.file-item');

    if (searchInput && fileItems.length > 0) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            let visibleCount = 0;

            fileItems.forEach(function(item) {
                const fileTitle = item.querySelector('.file-title').textContent.toLowerCase();
                const detailValues = Array.from(item.querySelectorAll('.detail-value')).map(el => el.textContent.toLowerCase());
                const allText = [fileTitle, ...detailValues].join(' ');

                if (searchTerm === '' || allText.includes(searchTerm)) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            // Update search results indicator
            updateSearchResults(visibleCount, fileItems.length, searchTerm);
        });
    }
});

function updateSearchResults(visibleCount, totalCount, searchTerm) {
    // Remove existing search results indicator
    const existingIndicator = document.querySelector('.search-results-indicator');
    if (existingIndicator) {
        existingIndicator.remove();
    }

    // Add new search results indicator if searching
    if (searchTerm) {
        const indicator = document.createElement('div');
        indicator.className = 'search-results-indicator';
        indicator.style.cssText = `
            padding: 1rem 2rem;
            background: var(--primary-light);
            border-bottom: 1px solid var(--border-color);
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.875rem;
        `;
        indicator.innerHTML = `
            <i class="fas fa-search me-2"></i>
            Showing ${visibleCount} of ${totalCount} files matching "${searchTerm}"
            ${visibleCount === 0 ? '<span style="color: var(--warning-color); margin-left: 1rem;"><i class="fas fa-exclamation-triangle me-1"></i>No matches found</span>' : ''}
        `;

        const filesList = document.querySelector('.files-list');
        if (filesList) {
            filesList.parentNode.insertBefore(indicator, filesList);
        }
    }
}

// Highlight search terms in results
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark style="background: yellow; padding: 0.1rem 0.2rem; border-radius: 0.2rem;">$1</mark>');
}
</script>
{% endblock %}
